# -*- coding: utf-8 -*-

from __future__ import annotations

import time
import logging
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple

from PyQt6.QtCore import QTimer, pyqtSignal
from PyQt6.QtWidgets import QWidget

__all__ = ["ZenohWidget"]

logger = logging.getLogger("ZenohWidget")

class ZenohWidget(QWidget):
    """Base class for GUI widgets consuming Zenoh data."""

    # ---------------------------------------------------- Qt signals
    data_updated = pyqtSignal(str, object)        # source_name, value
    data_freshness_changed = pyqtSignal(str, bool)  # source_name, is_fresh

    # ---------------------------------------------------- construction
    def __init__(
        self,
        core: Any,
        *args,
        data_timeout: float = 5.0,
        fixed_namespace: Optional[str] = None,
        **kwargs,
    ) -> None:
        super().__init__(*args, **kwargs)
        self.core = core
        self.zenoh = getattr(core, "zenoh_client", None)

        self.data_sources: Dict[str, Dict[str, Any]] = {}
        self._handles: List[Any] = []            # SubscriptionHandle list
        self.current_namespace: Optional[str] = None

        # Service-related data
        self._service_cache: Dict[str, Any] = {}  # Cache for service responses
        self._service_timers: Dict[str, QTimer] = {}  # Periodic service call timers

        # "Sticky" namespace support ------------------------------
        # If *fixed_namespace* is provided, the widget will always
        # subscribe to that namespace and ignore global vehicle
        # changes coming from *core.vehicle_changed*.
        self.fixed_namespace: Optional[str] = fixed_namespace

        # Freshness tracking --------------------------------------
        self.data_timeout = data_timeout
        self.last_update_time: Dict[str, float] = {}
        self.data_is_fresh: Dict[str, bool] = {}
        self.freshness_timer = QTimer(self)
        self.freshness_timer.timeout.connect(self._check_data_freshness)

        # React to namespace changes ---------------------------------
        # Widgets with fixed namespace do not need to listen for
        # global vehicle changes.
        if self.fixed_namespace is None and hasattr(self.core, "vehicle_changed"):
            self.core.vehicle_changed.connect(self.on_namespace_changed)

        # Each widget may implement specialised handlers -------------
        self.data_updated.connect(self._dispatch_update)

    # ---------------------------------------------------- namespace helpers
    def _get_current_namespace(self) -> Optional[str]:
        """Return the namespace this widget should use.

        If *fixed_namespace* was specified at construction/prepare time,
        that value is returned unconditionally, effectively making the
        widget "sticky" to a particular vehicle. Otherwise the namespace
        is resolved from the *core* object as before.
        """
        if self.fixed_namespace is not None:
            return self.fixed_namespace

        in_rc = getattr(self.core, "in_rc_vehid", None)
        watched = getattr(self.core, "watched_vehid", None)
        ns = in_rc or watched
        if ns and hasattr(self.core, "veh_active_states"):
            if not self.core.veh_active_states.get(ns, False):
                ns = None
        return ns

    def on_namespace_changed(self):
        new_ns = self._get_current_namespace()
        if new_ns == self.current_namespace:
            return
        self.unsubscribe()
        self.current_namespace = new_ns
        if self.current_namespace and self.zenoh:
            self.subscribe()
        # mark all sources stale so UI can grey‑out
        for src in self.data_sources:
            self._set_data_freshness(src, False)

    # ---------------------------------------------------- runtime API
    def prepare(self, *,
                data_sources: Optional[Dict[str, Dict[str, Any]]] = None,
                data_timeout: Optional[float] = None,
                fixed_namespace: Optional[str] = None,
                **_: Any):
        if data_sources is not None:
            self.data_sources = data_sources
            for src in data_sources:
                self.data_is_fresh[src] = False

        if data_timeout is not None:
            self.data_timeout = data_timeout

        # Allow supplying fixed namespace via *prepare()* as well
        # (overrides constructor value).
        if fixed_namespace is not None:
            self.fixed_namespace = fixed_namespace
            # Stop listening to global namespace changes if we switch
            # to sticky behaviour at runtime.
            if hasattr(self.core, "vehicle_changed"):
                try:
                    self.core.vehicle_changed.disconnect(self.on_namespace_changed)
                except Exception:
                    pass

        # Setup dependencies for intermediate sources
        self._dependencies: Dict[str, List[Tuple[str, str]]] = {}
        for src_name, cfg in self.data_sources.items():
            if "source" in cfg:
                base_src = cfg["source"]
                field_path = cfg.get("field", "")
                self._dependencies.setdefault(base_src, []).append((src_name, field_path))

        # Connect handler for derived sources if we have any dependencies
        if self._dependencies:
            # Disconnect first to avoid duplicate connections
            try:
                self.data_updated.disconnect(self._process_intermediate_update)
            except:
                pass
            self.data_updated.connect(self._process_intermediate_update)

    def start(self):
        self.current_namespace = self._get_current_namespace()
        if self.current_namespace and self.zenoh:
            self.subscribe()
        self.freshness_timer.start(1000)

    def cleanup(self):
        """Clean up resources when widget is destroyed."""
        logger.debug("Cleaning up ZenohWidget resources")

        # Stop freshness timer
        if hasattr(self, 'freshness_timer') and self.freshness_timer.isActive():
            self.freshness_timer.stop()

        # Unsubscribe from topics and stop service timers
        self.unsubscribe()

        # Disconnect from zenoh signals
        if hasattr(self, '_connected_zenoh_id') and self.zenoh:
            try:
                self.zenoh.serviceResponseReceived.disconnect(self._on_service_response)
            except Exception:
                pass  # Already disconnected
            delattr(self, '_connected_zenoh_id')

    # ---------------------------------------------------- field extraction
    def _extract_field_value(self, data: Any, field_path: str) -> Any:
        """Extract value from nested data by field path.

        Supports:
        - Dotted paths for nested fields (e.g. "pose.position.x")
        - Array iteration with [] suffix (e.g. "points[].x")
        - Mixed paths (e.g. "segments[].points[].position.x")

        Args:
            data: Source data (message object, dict, or list)
            field_path: Dotted path with optional [] for arrays

        Returns:
            Extracted value (single value or list)
        """
        if not field_path:
            return data

        parts = field_path.split(".")
        values = [data] if not isinstance(data, (list, tuple)) else list(data)

        for part in parts:
            if not values:
                break

            # Check if this part requires iteration
            iterate = part.endswith("[]")
            field = part[:-2] if iterate else part

            new_values = []
            for item in values:
                if item is None:
                    continue

                # Extract field from object or dict
                if isinstance(item, dict):
                    attr_val = item.get(field)
                else:
                    try:
                        attr_val = getattr(item, field)
                    except AttributeError:
                        logger.debug(f"Field '{field}' not found in {type(item)}")
                        attr_val = None

                if attr_val is None:
                    continue

                if iterate:
                    # Expand list/tuple, or treat single value as one-item list
                    if isinstance(attr_val, (list, tuple)):
                        new_values.extend(attr_val)
                    else:
                        new_values.append(attr_val)
                else:
                    new_values.append(attr_val)

            values = new_values

        # Determine return format
        # If path had any [] or input was list, return list (even if empty)
        # Otherwise return single value or None
        has_iteration = any(part.endswith("[]") for part in parts)
        if has_iteration or isinstance(data, (list, tuple)):
            return values
        else:
            return values[0] if values else None

    def _process_intermediate_update(self, source_name: str, value: Any):
        """Process updates for intermediate/derived sources.

        When a base source updates, compute and emit updates for dependent sources.
        """
        if source_name not in getattr(self, "_dependencies", {}):
            return

        for target_name, field_path in self._dependencies[source_name]:
            try:
                # Extract derived value from the base source value
                derived_val = self._extract_field_value(value, field_path)

                # Apply multiplier if configured
                cfg = self.data_sources.get(target_name, {})
                multiplier = cfg.get("multiplier", 1.0)
                if multiplier != 1.0:
                    if isinstance(derived_val, (int, float)):
                        derived_val = derived_val * multiplier
                    elif isinstance(derived_val, list) and derived_val:
                        if all(isinstance(v, (int, float)) for v in derived_val):
                            derived_val = [v * multiplier for v in derived_val]

                logger.debug(f"Derived '{target_name}' from '{source_name}': {derived_val}")

                # Emit update for the derived source
                self.data_updated.emit(target_name, derived_val)
                self.last_update_time[target_name] = time.time()
                if not self.data_is_fresh.get(target_name):
                    self._set_data_freshness(target_name, True)

            except Exception as e:
                logger.error(f"Failed to derive {target_name} from {source_name}: {e}")

    # ---------------------------------------------------- subscription logic
    def subscribe(self):
        if not self.zenoh or not self.current_namespace:
            logger.warning(f"Cannot subscribe: zenoh={bool(self.zenoh)}, ns={self.current_namespace}")
            return
        self.unsubscribe()  # safety first

        # Clear service cache when changing namespace
        self._service_cache.clear()

        # Group topic-based sources
        grouped: Dict[Tuple[str, str], List[Tuple[str, str, float]]] = defaultdict(list)

        for src_name, cfg in self.data_sources.items():
            # Skip intermediate sources (they don't need direct subscription)
            if "source" in cfg:
                continue

            # Handle service-based sources
            if "service_name" in cfg:
                self._setup_service_source(src_name, cfg)
                continue

            # Handle topic-based sources
            try:
                grouped[(cfg["topic"], cfg["msg_type"])].append(
                    (src_name, cfg["field"], cfg.get("multiplier", 1.0))
                )
            except KeyError as e:
                logger.error(f"Incomplete cfg for {src_name}: missing {e}")

        for (topic, msg_type), fields in grouped.items():
            logger.debug(f"Subscribing to {topic} ({msg_type}) in namespace {self.current_namespace}")

            def _handler(_key: str, msg: object, *, _fields=fields):
                logger.debug(f"Received message on {_key}: {msg}")
                for s_name, field_path, k in _fields:
                    try:
                        # Extract nested field value using new method
                        val = self._extract_field_value(msg, field_path)

                        # Apply multiplier to numeric values
                        if k != 1.0:
                            if isinstance(val, (int, float)):
                                val = val * k
                            elif isinstance(val, list) and val:
                                # Apply multiplier to numeric lists
                                if all(isinstance(v, (int, float)) for v in val):
                                    val = [v * k for v in val]

                        logger.debug(f"  - Extracted field '{field_path}' for source '{s_name}': {val}")
                        self.data_updated.emit(s_name, val)
                        self.last_update_time[s_name] = time.time()
                        if not self.data_is_fresh.get(s_name):
                            self._set_data_freshness(s_name, True)
                    except Exception as e:
                        logger.error(f"Cannot extract field '{field_path}' from message: {e}")

            handle = self.zenoh.subscribe(topic, msg_type, self.current_namespace, callback=_handler)
            self._handles.append(handle)

    def unsubscribe(self):
        if not self.zenoh:
            return
        for h in self._handles:
            try:
                self.zenoh.unsubscribe(h)
            except Exception as e:
                logger.error(f"Error during unsubscribe: {e}")
        self._handles.clear()

        # Stop and clear service timers
        for timer in self._service_timers.values():
            timer.stop()
        self._service_timers.clear()

    # ---------------------------------------------------- service sources
    def _setup_service_source(self, src_name: str, cfg: Dict[str, Any]) -> None:
        """Setup a service-based data source.

        Args:
            src_name: Name of the data source
            cfg: Configuration containing service_name, service_pkg, service_type, field, etc.
        """
        logger.debug(f"Setting up service source '{src_name}' for service {cfg['service_name']}")

        # Setup periodic calling if interval is specified
        interval = cfg.get("call_interval_sec")
        if interval and interval > 0:
            timer = QTimer(self)
            # Create a closure that captures values by value, not by reference
            def timer_callback(source_name=src_name, source_cfg=cfg):
                self._call_service_for_source(source_name, source_cfg)
            timer.timeout.connect(timer_callback)
            timer.start(int(interval * 1000))  # Convert to milliseconds
            self._service_timers[src_name] = timer
            logger.debug(f"Service '{src_name}' will be called every {interval} seconds")

        # Note: Service is NOT called automatically on setup
        # Widget code should call call_service_source() when needed

    def call_service_source(self, src_name: str, **kwargs) -> bool:
        """Call a configured service source manually.

        Args:
            src_name: Name of the service source from data_sources
            **kwargs: Additional request parameters

        Returns:
            True if service call was initiated, False otherwise
        """
        if src_name not in self.data_sources:
            logger.error(f"Service source '{src_name}' not configured")
            return False

        cfg = self.data_sources[src_name]
        if "service_name" not in cfg:
            logger.error(f"Source '{src_name}' is not a service source")
            return False

        return self._call_service_for_source(src_name, cfg, **kwargs)

    def _ensure_service_response_connected(self) -> None:
        """Ensure service response handler is connected to current zenoh client."""
        if not self.zenoh:
            return

        # Check if we need to connect/reconnect
        current_zenoh_id = id(self.zenoh)
        if not hasattr(self, '_connected_zenoh_id') or self._connected_zenoh_id != current_zenoh_id:
            # Disconnect from old zenoh client if exists
            if hasattr(self, '_connected_zenoh_id'):
                logger.debug("Reconnecting service response handler to new zenoh client")

            try:
                self.zenoh.serviceResponseReceived.connect(self._on_service_response)
                self._connected_zenoh_id = current_zenoh_id
                logger.debug("Service response handler connected")
            except Exception as e:
                logger.error(f"Failed to connect service response handler: {e}")

    def _call_service_for_source(self, src_name: str, cfg: Dict[str, Any], **kwargs) -> bool:
        """Call a service and process the response for a data source.

        Args:
            src_name: Name of the data source
            cfg: Service configuration
            **kwargs: Additional request parameters

        Returns:
            True if service call was initiated, False otherwise
        """
        if not self.zenoh:
            # For periodic calls, log less frequently to avoid spam
            if cfg.get("call_interval_sec"):
                logger.debug(f"Cannot call periodic service for {src_name}: no Zenoh client")
            else:
                logger.warning(f"Cannot call service for {src_name}: no Zenoh client")
            return False

        if not self.current_namespace:
            # For periodic calls, log less frequently to avoid spam
            if cfg.get("call_interval_sec"):
                logger.debug(f"Cannot call periodic service for {src_name}: no namespace")
            else:
                logger.warning(f"Cannot call service for {src_name}: no namespace")
            return False

        try:
            # Call service asynchronously
            self.zenoh.call_service_async(
                service_name=cfg["service_name"],
                service_pkg=cfg["service_pkg"],
                service_type=cfg["service_type"],
                namespace=self.current_namespace,
                **kwargs
            )

            # Connect response handler (ensure it's connected to current zenoh client)
            self._ensure_service_response_connected()

            return True

        except Exception as e:
            # For periodic calls, log less frequently to avoid spam
            if cfg.get("call_interval_sec"):
                logger.debug(f"Error calling periodic service for {src_name}: {e}")
            else:
                logger.error(f"Error calling service for {src_name}: {e}")
            return False

    def _on_service_response(self, service_name: str, response: Any) -> None:
        """Handle service response and extract data for configured sources.

        Args:
            service_name: Name of the service that responded
            response: Service response object
        """
        # Find all sources that use this service
        for src_name, cfg in self.data_sources.items():
            if cfg.get("service_name") == service_name:
                try:
                    # Extract field from response using existing field extraction logic
                    field_path = cfg.get("field", "")
                    value = self._extract_field_value(response, field_path)

                    # Apply multiplier if configured
                    multiplier = cfg.get("multiplier", 1.0)
                    if multiplier != 1.0 and isinstance(value, (int, float)):
                        value = value * multiplier
                    elif multiplier != 1.0 and isinstance(value, list) and value:
                        if all(isinstance(v, (int, float)) for v in value):
                            value = [v * multiplier for v in value]

                    # Cache the response
                    self._service_cache[src_name] = value

                    logger.debug(f"Service response for '{src_name}': {value}")

                    # Emit update
                    self.data_updated.emit(src_name, value)
                    self.last_update_time[src_name] = time.time()
                    if not self.data_is_fresh.get(src_name):
                        self._set_data_freshness(src_name, True)

                except Exception as e:
                    logger.error(f"Error processing service response for {src_name}: {e}")

    # ---------------------------------------------------- publishing helper
    def publish(self, topic: str, msg_type: str, namespace: Optional[str] = None, **kwargs):
        """Publish a message to Zenoh.

        Args:
            topic: Topic name
            msg_type: Message type (e.g. 'drill_msgs/msg/Permission')
            namespace: Namespace to use (defaults to current namespace)
            **kwargs: Additional message fields

        Returns:
            True if message was published, False otherwise
        """
        if not self.zenoh:
            logger.warning("Cannot publish: no Zenoh client")
            return False

        ns = namespace or self.current_namespace
        if not ns:
            logger.warning(f"Cannot publish to topic {topic}: no namespace")
            return False

        try:
            self.zenoh.publish(
                key_expr=topic,
                msg_type=msg_type,
                namespace=ns,
                **kwargs
            )
            return True
        except Exception as e:
            logger.error(f"Error publishing to {topic} in namespace {ns}: {e}")
            return False



    # ---------------------------------------------------- data freshness
    def _set_data_freshness(self, source: str, fresh: bool):
        if self.data_is_fresh.get(source) != fresh:
            logger.debug(f"Source '{source}' in ns '{self.current_namespace}' freshness changed: {fresh}")
            self.data_is_fresh[source] = fresh
            self.data_freshness_changed.emit(source, fresh)

    def _check_data_freshness(self):
        now = time.time()
        for src, t in list(self.last_update_time.items()):
            if now - t > self.data_timeout and self.data_is_fresh.get(src, False):
                logger.debug(f"Source {src} in ns '{self.current_namespace}' is stale: {now - t:.1f}s > {self.data_timeout}s")
                self._set_data_freshness(src, False)

    # ---------------------------------------------------- dispatch helpers
    def _dispatch_update(self, source_name: str, value: Any):
        logger.debug(f"Dispatching update for '{source_name}' in ns '{self.current_namespace}': {value}")

        # Apply any transforms defined in data_sources
        if source_name in self.data_sources and "transform" in self.data_sources[source_name]:
            transform = self.data_sources[source_name]["transform"]
            transform_type = transform.get("type")

            if transform_type == "equals":
                # Check if the value equals a specified value
                value = value == transform.get("value")
            elif transform_type == "not_equals":
                # Check if the value does not equal a specified value
                value = value != transform.get("value")
            elif transform_type == "in":
                # Check if the value is in a list of values
                value = value in transform.get("values", [])
            elif transform_type == "not_in":
                # Check if the value is not in a list of values
                value = value not in transform.get("values", [])

            logger.debug(f"Applied transform '{transform_type}' to '{source_name}', result: {value}")

        # fallback generic handler hook for concrete widgets
        if hasattr(self, "handle_update"):
            self.handle_update(source_name, value)
        spc = getattr(self, f"handle_{source_name}", None)
        if callable(spc):
            spc(value)

    # ---------------------------------------------------- convenience
    def is_data_fresh(self, source: Optional[str] = None) -> bool:
        if source is not None:
            return self.data_is_fresh.get(source, False)
        return bool(self.data_is_fresh) and all(self.data_is_fresh.values())

    def cleanup(self):
        self.freshness_timer.stop()
        self.unsubscribe()
        if hasattr(self.core, "vehicle_changed"):
            try:
                self.core.vehicle_changed.disconnect(self.on_namespace_changed)
            except Exception:
                pass
