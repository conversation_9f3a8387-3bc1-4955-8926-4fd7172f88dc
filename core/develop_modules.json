{"communicator": {"is_active": true, "file_name": "core", "class_name": "Communicator", "init_args": {}, "prep_args": {}}, "zenoh_client": {"is_active": true, "file_name": "zenoh_client", "class_name": "ZenohClient", "init_args": {"msg_dirs": "/Users/<USER>/Work/drill2/onboard/src/drill_msgs/msg"}, "prep_args": {}}, "joystick_adapter": {"is_active": true, "file_name": "joystick_adapter", "class_name": "JoystickAdapter", "init_args": {}, "prep_args": {"port": ["/dev/ttyACM0", "/dev/ttyACM1", "/dev/ttyACM2", "/dev/ttyACM3", "/dev/ttyACM4"], "baudrate": 115200, "pkg_size": 49, "pkg_timeout": 0.5, "udp_ip": "0.0.0.0", "udp_port": 2000, "joystick_power_coef": 1, "channel_thresholds": {"joystick1": [0, 1.44, 2.95], "joystick2": [0, 1.46, 2.96], "joystick3": [0.05, 1.51, 2.95], "pot_value": [0, 0, 2.95]}, "switches_state_map": {"joystick1_button": {"1": false, "0": true}, "joystick2_button": {"1": false, "0": true}, "joystick3_button": {"1": false, "0": true}, "button_pair": {"1": 1, "11": 0, "10": -1, "0": 0}, "switch": {"1": -1, "11": 0, "10": 1, "0": 0}}, "channel_name_map": {"switch_value": "switch", "joystick1": "joystick1", "joystick2": "joystick2", "joystick3": "joystick3", "button_pair": "button_pair", "pot_value": "pot_value"}, "channel_to_index_map": {"joystick1_button": 6, "joystick2_button": 8, "joystick3_button": 11, "button_pair": [5, 7], "switch": [9, 10], "joystick1": [19, 20, 21, 22], "joystick2": [23, 24, 25, 26], "joystick3": [27, 28, 29, 30], "pot_value": [31, 32, 33, 34]}, "no_lock_modes": ["driving", "leveling"], "layouts": {"driving": {"tracks_control_remote": {"msg_type": "drill_msgs/msg/TracksCtrl", "fields": {"left": "-joystick1", "right": "-joystick3"}}, "jacks_control_remote": {"msg_type": "drill_msgs/msg/JacksCtrl", "fields": {"rear": "-button_pair", "right": "-button_pair", "left": "-button_pair"}}}, "drilling": {"drill_control_remote": {"msg_type": "drill_msgs/msg/DrillActuatorCtrl", "fields": {"rotation_speed": "joystick1", "feed_speed": "joystick3", "feed_pressure": "pot_value", "torque_limit": 1.0}}, "compressor_control_remote": {"msg_type": "drill_msgs/msg/CompressorCtrl", "fields": {"state": "switch", "power": 1.0}}, "arm_control_remote": {"msg_type": "drill_msgs/msg/FloatCtrl", "fields": {"ctrl": "joystick2"}}, "dust_flaps_control_remote": {"msg_type": "drill_msgs/msg/FloatCtrl", "fields": {"ctrl": "-button_pair"}}}, "leveling": {"jacks_control_remote": {"msg_type": "drill_msgs/msg/JacksCtrl", "fields": {"rear": "joystick2", "right": "joystick3", "left": "joystick1"}}}, "tower_control": {"tower_control_remote": {"msg_type": "drill_msgs/msg/TowerCtrl", "fields": {"incl_pins": "-joystick1@0.5", "vert_pins": "-joystick3@0.5", "tilt": "joystick2"}}, "arm_control_remote": {"msg_type": "drill_msgs/msg/FloatCtrl", "fields": {"ctrl": "button_pair"}}}, "buildup": {"drill_control_remote": {"msg_type": "drill_msgs/msg/DrillActuatorCtrl", "fields": {"rotation_speed": "joystick1", "feed_pressure": "pot_value", "feed_speed": "joystick3", "torque_limit": 1.0}}, "wrench_control_remote": {"msg_type": "drill_msgs/msg/WrenchCtrl", "fields": {"swing": "joystick2", "grip": "-button_pair"}}, "fork_control_remote": {"msg_type": "drill_msgs/msg/FloatCtrl", "fields": {"ctrl": "-switch"}}}, "carousel_control": {"carousel_control_remote": {"msg_type": "drill_msgs/msg/CarouselCtrl", "fields": {"swing": "joystick2", "index": "button_pair"}}, "drill_control_remote": {"msg_type": "drill_msgs/msg/DrillActuatorCtrl", "fields": {"rotation_speed": "joystick1", "feed_pressure": "pot_value", "feed_speed": "joystick3", "torque_limit": 1.0}}, "arm_control_remote": {"msg_type": "drill_msgs/msg/FloatCtrl", "fields": {"ctrl": "switch"}}}}}}, "plan_reader": {"is_active": true, "file_name": "plan_reader", "class_name": "PlanReader", "init_args": {}, "prep_args": {}}, "core_data_translator": {"is_active": false, "file_name": "gui_modules", "class_name": "CoreDataTranslator", "init_args": {}, "prep_args": {}}, "map": {"is_active": true, "file_name": "gui_modules", "class_name": "Map", "init_args": {}, "prep_args": {"width": 350, "height": 400, "x": 0, "y": 0, "screen_num": 1, "data_sources": {"position_x": {"topic": "position", "msg_type": "drill_msgs/msg/Position", "field": "x"}, "position_y": {"topic": "position", "msg_type": "drill_msgs/msg/Position", "field": "y"}, "yaw": {"topic": "position", "msg_type": "drill_msgs/msg/Position", "field": "yaw"}, "driver_action_id": {"topic": "driver_status", "msg_type": "drill_msgs/msg/DriveStatus", "field": "cur_action_id"}, "driver_segment_id": {"topic": "driver_status", "msg_type": "drill_msgs/msg/DriveStatus", "field": "cur_segment_id"}, "driver_point_id": {"topic": "driver_status", "msg_type": "drill_msgs/msg/DriveStatus", "field": "cur_point_id"}, "obstacles_vis": {"topic": "obstacles_vis", "msg_type": "visualization_msgs/msg/MarkerArray", "field": "markers"}, "safety_berm_map": {"topic": "safety_berm_map", "msg_type": "visualization_msgs/msg/MarkerArray", "field": "markers"}, "border_vis": {"topic": "border_vis", "msg_type": "visualization_msgs/msg/MarkerArray", "field": "markers"}}, "service_configs": {"get_routes": {"service_name": "/get_current_drive_action", "service_pkg": "drill_msgs", "service_type": "GetCurrentDriveAction"}}}}, "front_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 785, "height": 540, "x": 175, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 2, "vflip": true, "hflip": true}}}, "prep_args": {"cam_id": "6"}}, "birdview_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"birdview": {"width": 785, "height": 540, "x": 175, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 180, "screen": 2, "vflip": true, "hflip": true}}}, "prep_args": {"cam_id": "1"}}, "rear_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"rear": {"width": 785, "height": 540, "x": 175, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 180, "screen": 2}}}, "prep_args": {"cam_id": "5"}}, "right_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 720, "height": 540, "x": 1745, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "vflip": true, "rotation": 0, "screen": 2}, "birdview": {"width": 720, "height": 540, "x": 1745, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "vflip": true, "rotation": 0, "screen": 2}, "rear": {"width": 720, "height": 540, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1, "vflip": true}, "drill": {"width": 632, "height": 540, "x": 656, "y": 0, "crop": [0, 0.1, 0.0, 0.1], "rotation": 0, "screen": 1, "vflip": true, "hflip": true}}}, "prep_args": {"cam_id": "3"}}, "drill_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"drill": {"width": 405, "height": 540, "x": 300, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}}}, "prep_args": {"cam_id": "2"}}, "tower_cam": {"is_active": false, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"drill": {"width": 405, "height": 540, "x": 1110, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 90, "screen": 2}}}, "prep_args": {"cam_id": "7"}}, "left_cam": {"is_active": true, "file_name": "gui_modules", "class_name": "Cameras", "init_args": {"modes": {"front": {"width": 720, "height": 540, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1, "vflip": true}, "birdview": {"width": 720, "height": 540, "x": 656, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 1, "vflip": true}, "rear": {"width": 720, "height": 540, "x": 1745, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 0, "screen": 2, "vflip": true}, "drill": {"width": 632, "height": 540, "x": 0, "y": 0, "crop": [0, 0.0, 0.0, 0.0], "rotation": 180, "screen": 3}}}, "prep_args": {"cam_id": "4"}}, "dial_indicator_rot_speed": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 150, "height": 150, "x": 200, "y": 310, "field_to_read": "drill_rotation_speed", "initial_value": 25, "ticks_font_size": 10, "scale_range": 14, "scale_step": 2, "caption": "Revolutions\n(revs/min) x10", "scale_gradient": {"green": 0.34, "yellow": 0.3, "red": 0.15}, "caption_font_size": 7, "coeff": 0.1, "digit_value_coeff": 10, "modes_visible": ["drill"], "screen_num": 1}}, "dial_indicator_air_pressure": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 150, "height": 150, "x": 400, "y": 310, "field_to_read": "air_pressure", "initial_value": 150, "ticks_font_size": 9, "caption_font_size": 7, "scale_range": 150, "scale_step": 25, "caption": "Air\npressure (psi)", "scale_gradient": {"green": 0.7, "yellow": 0.63, "red": 0.4}, "coeff": 14.504, "modes_visible": ["drill"], "screen_num": 1}}, "dial_indicator_rot_pressure": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 150, "height": 150, "x": 600, "y": 310, "field_to_read": "drill_rotation_pressure", "initial_value": 7500, "ticks_font_size": 9, "scale_range": 12, "scale_step": 2, "caption": "Rotation torque\n(klb*ft)", "caption_font_size": 7, "scale_gradient": {"green": 0.6, "yellow": 0.42, "red": 0.25}, "coeff": 0.031, "coeff2": 14.503, "units2": "psi", "modes_visible": ["drill"], "screen_num": 1}}, "dial_indicator_feed_pressure": {"is_active": true, "file_name": "gui_modules", "class_name": "DialIndicator", "init_args": {}, "prep_args": {"width": 150, "height": 150, "x": 800, "y": 310, "field_to_read": "drill_feed_pressure", "initial_value": 7500, "ticks_font_size": 9, "caption_font_size": 7, "scale_range": 80, "scale_step": 10, "caption": "Drill Feed\npressure (klbf)", "scale_gradient": {"green": 0.65, "yellow": 0.5, "red": 0.25}, "coeff": 0.22, "coeff2": 14.503, "units2": "psi", "modes_visible": ["drill"], "screen_num": 1}}, "angle_indicator": {"is_active": true, "file_name": "gui_modules", "class_name": "AngleIndicator", "init_args": {}, "prep_args": {"width": 300, "height": 300, "x": 50, "y": 500, "label_font_size": 12, "screen_num": 1, "data_sources": {"pitch": {"topic": "level", "msg_type": "drill_msgs/msg/Level", "field": "pitch"}, "roll": {"topic": "level", "msg_type": "drill_msgs/msg/Level", "field": "roll"}, "mast_angle": {"topic": "tower_inclinometer", "msg_type": "drill_msgs/msg/Vector2d", "field": "x"}}}}, "rear_jack_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 60, "x": 116, "y": 831, "screen": 1, "field_to_read": "rear_jack_pulled", "second_field_to_read": "rear_jack_on_ground", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "right_jack_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 60, "x": 59, "y": 910, "screen": 1, "field_to_read": "right_jack_pulled", "second_field_to_read": "right_jack_on_ground", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "left_jack_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 60, "x": 167, "y": 912, "screen": 1, "field_to_read": "left_jack_pulled", "second_field_to_read": "left_jack_on_ground", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "carousel_position_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 660, "screen": 1, "static_label": "Position", "field_to_read": "carousel_open", "second_field_to_read": "carousel_closed", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Open", "off_text": "Closed", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "carousel_index_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 684, "screen": 1, "static_label": "Index", "field_to_read": "carousel_index_1", "second_field_to_read": "carousel_index_2", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Index 1", "off_text": "Index 2", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "fork_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 939, "screen": 1, "static_label": "U-Fork", "field_to_read": "fork_extended", "second_field_to_read": "fork_retracted", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Extended", "off_text": "Retracted", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "arm_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 971, "screen": 1, "static_label": "Rod support", "field_to_read": "arm_open", "second_field_to_read": "arm_grip", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Opened", "off_text": "Closed", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "wrench_position_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 757, "screen": 1, "static_label": "Position", "field_to_read": "wrench_engaged", "second_field_to_read": "wrench_stowed", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Engaged", "off_text": "Stowed", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "wrench_grip_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 781, "screen": 1, "static_label": "<PERSON><PERSON>", "field_to_read": "wrench_grip", "second_field_to_read": "wrench_open", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Gripped", "off_text": "Released", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "vert_pin_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 853, "screen": 1, "static_label": "Vertical", "field_to_read": "vert_pin_lock", "second_field_to_read": "vert_pin_release", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Released", "off_text": "Locked", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "incl_pin_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 876, "screen": 1, "static_label": "Slanted", "field_to_read": "incl_pin_lock", "second_field_to_read": "incl_pin_release", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Released", "off_text": "Locked", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "dust_flaps_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 20, "x": 450, "y": 1003, "screen": 1, "static_label": "Dust flaps", "field_to_read": "dust_flaps_closed", "second_field_to_read": "dust_flaps_open", "from_telemetry": true, "on_color": "#FF0000", "off_color": "#00ff00", "intermediate_color": "#FFA500", "disabled_color": "#AAAAAA", "dynamic_label": true, "on_text": "Closed", "off_text": "Opened", "intermediate_text": "Moving", "disabled_text": "No data", "label_width": 85}}, "carousel_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 216, "height": 95, "x": 350, "y": 635, "border_color": "#444444", "border_width": 1.6, "border_radius": 8, "screen": 1, "is_label": true, "label_text": "Carousel", "label_font_size": "16px", "label_font_color": "#FFFFFF"}}, "wrench_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 216, "height": 95, "x": 350, "y": 733, "border_color": "#444444", "border_width": 1.6, "border_radius": 8, "screen": 1, "is_label": true, "label_text": "<PERSON><PERSON>", "label_font_size": "16px", "label_font_color": "#FFFFFF"}}, "common_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 216, "height": 110, "x": 350, "y": 937, "border_color": "#444444", "border_width": 1.6, "border_radius": 8, "screen": 1}}, "pins_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 216, "height": 95, "x": 350, "y": 831, "border_color": "#444444", "border_width": 1.6, "border_radius": 8, "screen": 1, "is_label": true, "label_text": "Pins", "label_font_color": "#FFFFFF", "label_font_size": "16px"}}, "jacks_category_container": {"is_active": true, "file_name": "gui_modules", "class_name": "CategoryContainer", "init_args": {}, "prep_args": {"width": 208, "height": 208, "x": 70, "y": 820, "border_color": "#444444", "border_width": 1.6, "border_radius": 8, "screen": 1, "is_label": true, "label_text": "<PERSON><PERSON>", "label_font_color": "#FFFFFF", "label_font_size": "22px"}}, "permission_indicator": {"is_active": true, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 30, "x": 508, "y": 489, "screen": 1, "field_to_read": "move_permission", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "gps_indicator": {"is_active": true, "file_name": "gui_modules", "class_name": "GPSWidget", "init_args": {}, "prep_args": {"r": 40, "x": 300, "y": 3, "screen": 1}}, "hole_dist_indicator": {"is_active": false, "file_name": "gui_modules", "class_name": "Distance2HoleWidget", "init_args": {"text": "Hole distance "}, "prep_args": {"width": 240, "height": 40, "x": 10, "y": 380, "font_size": 30, "screen_num": 1}}, "jacks_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "<PERSON><PERSON>", "font_size": "22px", "x": 63, "y": 790, "font_color": "#FFFFFF", "screen": 1}}, "tower_ok_widget": {"is_active": false, "file_name": "gui_modules", "class_name": "CircleWidget", "init_args": {}, "prep_args": {"r": 30, "x": 35, "y": 720, "screen": 1, "field_to_read": "tower_ok_to_lock", "from_telemetry": true, "on_color": "#00ff00", "off_color": "#FF0000"}}, "tower_ok_label": {"is_active": false, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Tower is in position", "font_size": "19px", "x": 50, "y": 780, "font_color": "#FFFFFF", "screen": 1, "left": true}}, "permission_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "Work permission", "x": 353, "y": 502, "font_color": "#FFFFFF", "screen": 1, "font_size": "20px"}}, "machine_state_management": {"is_active": true, "file_name": "gui_modules", "class_name": "MachineStateManagement", "init_args": {}, "prep_args": {"state_machine": {"MainStateMachineNode": {"Main State": {"idle": "Idle", "moving": "Driving", "leveling": "Leveling", "tower_tilt": "Tower tilt", "drilling": "Drilling", "shaft_buildup": "Shaft Build-up", "shaft_stow": "Shaft stow", "grounding": "Grounding", "remote_wait": "Wait for Remote", "remote_prepare": "Prepare for Remote", "remote": "In Remote", "end_remote": "Finishing Remote", "wait_after_level": "Waiting after leveling", "restore_string": "String restoring", "wait_before_level": "Waiting before leveling", "failure": "Failure", "init_check": "Initial Check"}}, "RodChangerNode": {"Rod changer": {"idle": "Idle", "align_cup": "Aligning rod for cup", "align_fork": "Aligning rod for fork", "approach_carousel": "Approaching rod for carousel", "approach_fork": "Approaching rod for fork", "apply_wrench": "Apply wrench", "brakeout": "Brak<PERSON><PERSON>", "close_arm": "Closing arm", "close_carousel": "Closing carousel", "close_wrench": "Closing wrench", "detach": "Detaching rod", "detach_cup": "Detaching rod in cup", "final_position": "Lift head to pull depth", "finish": "Finish", "insert": "Inserting rod in cup", "lift_to_carousel": "Lifting head to carousel", "lock": "Closing fork", "new_rod_screwing": "Screwing new rod", "open_arm": "Opening arm", "open_carousel": "Opening carousel", "open_forkarm": "Opening fork and arm", "open_wrench": "Opening wrench", "pull_out": "Pull out", "screwing": "Screwing", "turn_ccw": "Turn shaft ccw", "turn_cw": "Turn shaft cw", "turn_cw_cup": "Turn shaft cw in carousel cup", "unlock": "Opening fork", "failure": "Failure"}}, "ForkControlNode": {"Fork": {"opening": "Opening", "open": "Open", "closing": "Closing", "closed": "Close", "failure": "Failure", "missed": "Missed rod"}}, "CarouselControllerNode": {"Carousel": {"idle": "Idle", "turn_cw": "Turning CW", "turn_ccw": "Turning CCW", "closing": "Closing", "opening": "Opening", "failure": "Failure"}}, "WrenchControllerNode": {"Wrench": {"turn": "Turning", "release": "Releasing", "closing": "Closing", "opening": "Opening", "open": "Open", "closed": "Closed", "failure": "Failure"}}, "DrillerNode": {"Drilling": {"idle": "Idle", "touchdown": "Touchdown", "overburden_pass": "Overburden pass", "drilling": "Drilling", "hard_rot": "Hard Rot", "pullup": "<PERSON><PERSON><PERSON>", "after_pullup": "After Pullup", "raise": "String Raising", "failure": "Failure", "wait_after_drill": "Waiting after drill", "pass_soft": "Pass soft", "unstuck_down": "Unstuck Down", "unstuck_spin": "Unstuck Spin", "unstuck_up": "Unstuck Up", "unstuck": "Unstuck"}}, "ArmControllerNode": {"Arm": {"opening": "Opening", "open": "Open", "closing": "Closing", "closed": "Close", "failure": "Failure"}}, "DustFlapsControllerNode": {"Dust flaps": {"opening": "Opening", "open": "Open", "closing": "Closing", "closed": "Close", "failure": "Failure"}}, "LevelerNode": {"Jacks": {"init": "Init", "touchdown": "Lowering jacks", "pulling": "Pulling", "pulled": "Pulled", "restore_pulled": "Restoring pulled", "leveling": "Leveling", "final_leveling": "Final leveling", "lift": "Lifting up", "holding": "Holding", "failure": "Failure"}}, "PlannerNode": {"Planner": {"init": "Init", "idle": "Idle", "moving": "Moving", "approach": "Approach", "computing": "Computing", "failure": "Failure"}}}, "widget_params": {"x": 630, "y": 10, "width": 350, "height": 300, "color": "#D2D6E4", "font_size": "12px", "text_transform": "none", "font_weight": "normal"}, "confirm_params": {"color": "#fff", "background": "#4E5754", "background_ok": "#009B76", "background_no": "#E32636", "text_transform": "uppercase"}}}, "drill_speed_display": {"is_active": true, "file_name": "gui_modules", "class_name": "MovingDataTranslator", "init_args": {}, "prep_args": {"x": 1005, "y": 230, "width": 200, "height": 100, "screen_num": 1, "telemetry": {"drill_feed_speed": {"name": "Drill feed speed", "units": "m/min", "scale": 60, "smooth_k": 0.02}}, "lbl_name_params": {"color": "#D2D6E4", "font_size": "12px", "text_transform": "none", "font_weight": "normal"}, "lbl_value_params": {"color": "#D2D6E4", "font_size": "13px", "text_transform": "none", "font_weight": "bold"}, "mode_visible": ["drill"], "value_on_new_line": false}}, "telemetry_display": {"is_active": true, "file_name": "gui_modules", "class_name": "MovingDataTranslator", "init_args": {}, "prep_args": {"x": 350, "y": 10, "width": 250, "height": 180, "screen_num": 1, "telemetry": {"engine_speed": {"name": "Engine", "units": "RPM", "scale": 1, "smooth_k": 1, "red_less": 1790}, "battery_potential": {"name": "Battery", "units": "V", "scale": 1, "smooth_k": 1, "red_less": 24}, "coolant_temperature": {"name": "Coolant temp", "units": "°", "scale": 1, "smooth_k": 1, "red_more": 90}, "water_level": {"name": "Water level", "units": "%", "scale": 100, "smooth_k": 1, "red_less": 0.2}, "fuel_level": {"name": "Fuel level", "units": "%", "scale": 100, "smooth_k": 1, "red_less": 0.2}}, "lbl_name_params": {"color": "#D2D6E4", "font_size": "14px", "text_transform": "none", "font_weight": "normal"}, "lbl_value_params": {"color": "#D2D6E4", "font_size": "16px", "text_transform": "none", "font_weight": "bold"}, "mode_visible": ["ALL"], "value_on_new_line": false, "no_fraction": true}}, "emg_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "HardEmergencyButton", "init_args": {}, "prep_args": {"x": 75, "y": 680, "width": 300, "height": 200, "btn_width": 300, "btn_height": 137, "btn_red_fontsize": "25px", "btn_green_fontsize": "24px", "screen_num": 0}}, "reboot_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "RebootButton", "init_args": {}, "prep_args": {"x": 50, "y": 920, "width": 280, "height": 100, "btn_width": 280, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "finish_buildup_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "FinishBuildupButton", "init_args": {}, "prep_args": {"x": 1725, "y": 65, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "finish_stow_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "FinishStowButton", "init_args": {}, "prep_args": {"x": 1725, "y": 170, "width": 180, "height": 100, "btn_width": 170, "btn_height": 80, "btn_fontsize": "25px", "screen_num": 0}}, "tgbot_module": {"is_active": false, "file_name": "tgbot", "class_name": "TgBotClass", "init_args": {}, "prep_args": {}}, "move_permission_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "move_permission", "sound_file": "sounds/move_perm_beep.wav", "from_telemetry": true, "inverse": false}}, "disconnect_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "veh_active_states", "sound_file": "sounds/disconnect_wookie.wav", "from_telemetry": false, "inverse": false}}, "connect_sound": {"is_active": false, "file_name": "make_sound", "class_name": "MakeSoundClass", "init_args": {}, "prep_args": {"field_to_read": "veh_active_states", "sound_file": "sounds/buttondigital.mp3", "from_telemetry": false, "inverse": true}}, "maneuver_processing_text": {"is_active": true, "file_name": "gui_modules", "class_name": "ConditionalTextWidget", "init_args": {}, "prep_args": {"text": "Maneuver in process", "font_size": "19px", "x": 20, "y": 310, "font_color": "#00FF00", "screen": 1, "left": true, "field_to_read": "maneuver_processing", "from_telemetry": true}}, "feed_press_slider": {"is_active": true, "file_name": "gui_modules", "class_name": "CustomSliderWrapper", "init_args": {}, "prep_args": {"width": 795, "height": 140, "x": 405, "y": 505, "caption": "Feed pressure", "param_name": "/DrillerNode/user_feed_pressure", "minValue": 17, "maxValue": 60, "units": "klbf", "unit_scale": 0.22}}, "rot_speed_slider": {"is_active": true, "file_name": "gui_modules", "class_name": "CustomSliderWrapper", "init_args": {}, "prep_args": {"width": 795, "height": 140, "x": 405, "y": 660, "caption": "Rotation speed", "param_name": "/DrillerNode/user_rotation_speed", "minValue": 20, "maxValue": 110, "units": "rpm"}}, "test_depth_meter": {"is_active": true, "file_name": "gui_modules", "class_name": "DepthWidgetWrapper", "init_args": {}, "prep_args": {"width": 180, "height": 250, "x": 1000, "y": 10, "screen_num": 1, "dictToRead": "telemetry", "actualDepthField": "max_wellhead_drilling_depth", "targetDepthField": "target_wellhead_depth", "drillPosField": "wellhead_drilling_depth"}}, "Drill_joystick_controller": {"is_active": true, "file_name": "gui_modules", "class_name": "JoystickWidgetWrapper", "init_args": {}, "prep_args": {"width": 1200, "height": 500, "x": 400, "y": 0, "modes": {"driving": {"name": "Driving", "joy_mid": " ", "joy_mid_top": "", "joy_mid_bottom": "", "joy_left": "Right track", "joy_left_top": "Forth", "joy_left_bottom": "Back", "joy_right": "Left track", "joy_right_top": "Forth", "joy_right_bottom": "Back", "knob": " ", "buttons": "<PERSON><PERSON>", "tumbler": " ", "tumbler_top": "", "tumbler_mid": "", "tumbler_bottom": "", "green_button_name": "Retract", "red_button_name": "Extend"}, "drilling": {"name": "Drilling", "joy_mid": "Arm", "joy_mid_top": "Open", "joy_mid_bottom": "Close", "joy_left": "Rotation", "joy_left_top": "CCW", "joy_left_bottom": "CW", "joy_right": "Feed", "joy_right_top": "Up", "joy_right_bottom": "Down", "knob": "Feed force", "buttons": "Dust flaps", "tumbler": "Compressor", "tumbler_top": "On", "tumbler_mid": "Off", "tumbler_bottom": "Off", "green_button_name": "Open", "red_button_name": "Close"}, "leveling": {"name": "Leveling", "joy_mid": "Rear", "joy_mid_top": "Retract", "joy_mid_bottom": "Extend", "joy_left": "Right", "joy_left_top": "Retract", "joy_left_bottom": "Extend", "joy_right": "Left", "joy_right_top": "Retract", "joy_right_bottom": "Extend", "knob": " ", "buttons": "", "tumbler": " ", "tumbler_top": "", "tumbler_mid": "", "tumbler_bottom": "", "green_button_name": "", "red_button_name": ""}, "tower_control": {"name": "Tower", "joy_mid": "Tilt", "joy_mid_top": "Rai<PERSON>", "joy_mid_bottom": "Lower", "joy_left_top": "Extend", "joy_left_bottom": "Retract", "joy_right": "Vertical Pin", "joy_right_top": "Extend", "joy_right_bottom": "Retract", "joy_left": "Inclined <PERSON>n", "knob": " ", "buttons": "Arm", "tumbler": " ", "tumbler_top": "", "tumbler_mid": "", "tumbler_bottom": "", "green_button_name": "Open", "red_button_name": "Close"}, "buildup": {"name": "<PERSON><PERSON>", "joy_mid": "<PERSON><PERSON>", "joy_mid_top": "<PERSON><PERSON>", "joy_mid_bottom": "Swing in", "joy_left_top": "CCW", "joy_left_bottom": "CW", "joy_right": "Feed", "joy_right_top": "Up", "joy_right_bottom": "Down", "joy_left": "Rotation", "knob": "Feed force", "buttons": "<PERSON><PERSON>", "tumbler": "Fork", "tumbler_top": "Retract", "tumbler_mid": "Stop", "tumbler_bottom": "Extend", "green_button_name": "Release", "red_button_name": "Turn"}, "carousel_control": {"name": "Carousel", "joy_mid": "Carousel", "joy_mid_top": "Retract", "joy_mid_bottom": "Extend", "joy_left_top": "CCW", "joy_left_bottom": "CW", "joy_right": "Feed", "joy_right_top": "Up", "joy_right_bottom": "Down", "joy_left": "Rotation", "knob": "Feed force", "buttons": "Index", "tumbler": "Arm", "tumbler_top": "Open", "tumbler_mid": "Stop", "tumbler_bottom": "Close", "green_button_name": "CW", "red_button_name": "CCW"}}}}, "vehicle_selector_controller": {"is_active": true, "file_name": "gui_modules", "class_name": "VehicleSelector", "init_args": {"streams_cfg": {"main_state": {"topic": "main_state", "msg_type": "drill_msgs/msg/StateMachineStatus", "field": "current_state"}, "permission": {"topic": "permission", "msg_type": "drill_msgs/msg/Permission", "field": "permission"}, "robomode": {"topic": "robomode", "msg_type": "drill_msgs/msg/BoolStamped", "field": "value"}, "emergency_status": {"topic": "emergency_status", "msg_type": "drill_msgs/msg/BoolStamped", "field": "value"}, "planner_state": {"topic": "planner_state", "msg_type": "drill_msgs/msg/StateMachineStatus", "field": "current_state"}}, "publish_cfg": {"permission": {"topic": "permission", "msg_type": "drill_msgs/msg/Permission", "fields": {"permission": true, "source": "RMO"}}, "robomode": {"topic": "robomode_sp", "msg_type": "drill_msgs/msg/BoolStamped", "fields": {"value": true}}, "watch": {"topic": "set_state", "msg_type": "drill_msgs/msg/StateCommand", "fields": {"node_name": "MainStateMachine", "state": "END_REMOTE"}}, "control": {"topic": "set_state", "msg_type": "drill_msgs/msg/StateCommand", "fields": {"node_name": "MainStateMachine", "state": "REMOTE"}}, "disconnect": {"topic": "set_state", "msg_type": "drill_msgs/msg/StateCommand", "fields": {"node_name": "MainStateMachine", "state": "END_REMOTE"}}, "reset_emergency": {"topic": "reset_emergency", "msg_type": "drill_msgs/msg/EmergencyReset", "fields": {}}, "rmo_health": {"topic": "rmo_health", "msg_type": "drill_msgs/msg/RmoHealth", "fields": {}}}}, "prep_args": {"width": 400, "height": 480, "x": 0, "y": 0, "transition_timeout_sec": 3, "data_timeout_sec": 1, "health_update_sec": 1}}, "downtimes_button": {"is_active": true, "file_name": "downtimes", "class_name": "DowntimesButton", "init_args": {"text": "Register downtime", "fontSize": 14, "needConfirm": false}, "prep_args": {"width": 390, "height": 50, "x": 8, "y": 482}}, "auth_button": {"is_active": true, "file_name": "gui_modules", "class_name": "Auth<PERSON><PERSON><PERSON>", "init_args": {}, "prep_args": {"width": 285, "height": 138, "x": 1215, "y": 770, "login_color": "#171717", "logout_color": "#171717"}}, "drop_err_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropDrillError", "init_args": {"text": "Clear\nerror", "fontSize": 16, "needConfirm": false, "needPassword": false}, "prep_args": {"width": 180, "height": 40, "x": 1010, "y": 805}}, "drill_now_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "SendHoleButton", "init_args": {}, "prep_args": {"x": 395, "y": 790, "width": 195, "height": 150, "btn_width": 180, "btn_height": 40, "btn_fontsize": 16, "font_color": "#D2D6E4", "label_font_size": "16px", "screen_num": 0, "url": "/api/drill-now", "max_depth": 33}}, "drop_action_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropActionButton", "init_args": {"text": "Cancel\ntask", "fontSize": 16}, "prep_args": {"width": 180, "height": 40, "x": 606, "y": 805}}, "recalib_air_btn": {"is_active": false, "file_name": "gui_modules", "class_name": "RecalibAirButton", "init_args": {"text": "Recalibrate\nair pressure", "fontSize": 13, "needConfirm": true, "confirmationText": "Current air pressure will be set as nominal"}, "prep_args": {"width": 180, "height": 40, "x": 1010, "y": 850}}, "drop_tailing_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "DropTailingButton", "init_args": {"text": "Clear\ntailing", "fontSize": 16, "needConfirm": true, "confirmationText": "Detected tailing will be cleared"}, "prep_args": {"width": 180, "height": 40, "x": 808, "y": 850}}, "pullup_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "init_args": {"text": "Force\npullup", "fontSize": 16, "needConfirm": false}, "prep_args": {"width": 182, "height": 40, "x": 1010, "y": 850}}, "lights_switch": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartCheckbox", "init_args": {"text": "Lights", "paramName": "enable_lights", "fontSize": 16, "tracking": true}, "prep_args": {"width": 270, "height": 45, "x": 20, "y": 550}}, "dc_switch": {"is_active": false, "file_name": "customClasses.smartButton", "class_name": "SmartCheckbox", "init_args": {"text": "Water", "paramName": "dust_collector", "fontSize": 22, "tracking": true}, "prep_args": {"width": 270, "height": 60, "x": 20, "y": 630}}, "DriveDrillSwitch": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroup", "init_args": {"caption": "Operation mode", "buttons": {"Drill": "False", "Propel": "True"}, "defaultButton": "Drill", "captionSize": 20, "fontSize": 12, "colCount": 1, "paramName": "drivedrill", "tracking": true}, "prep_args": {"width": 300, "height": 150, "x": 10, "y": 760}}, "cameras_mode": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroupForCore", "init_args": {"caption": "Cameras mode", "buttons": {"Front": "front", "Rear": "rear", "Drilling": "drill", "Birdview": "birdview"}, "captionSize": 20, "fontSize": 12, "colCount": 1, "coreParam": "cameras_mode", "defaultButton": "Front"}, "prep_args": {"width": 200, "height": 240, "x": 1200, "y": 500}}, "photo_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "PhotoButton", "init_args": {"text": "Save images", "fontSize": 22, "needConfirm": false}, "prep_args": {"width": 40, "height": 40, "x": 1223, "y": 870}}, "rock_type": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroup", "init_args": {"caption": "Drill mode", "buttons": {"Normal": "", "Cracked": "cracked", "Hard": "hard"}, "captionSize": 18, "fontSize": 12, "colCount": 1, "paramName": "rock_type", "defaultButton": "Normal", "tracking": true}, "prep_args": {"width": 205, "height": 135, "x": 1395, "y": 500}}, "hole_water": {"is_active": true, "file_name": "customClasses.smartButton", "class_name": "SmartButtonGroup", "init_args": {"caption": "Wet mode", "buttons": {"Dry": "", "Flooded": "wet"}, "captionSize": 18, "fontSize": 12, "colCount": 1, "paramName": "hole_water", "defaultButton": "Dry", "tracking": true}, "prep_args": {"width": 205, "height": 100, "x": 1395, "y": 625}}, "messages_widget": {"is_active": true, "file_name": "gui_modules", "class_name": "MessagesWidget", "init_args": {}, "prep_args": {"width": 625, "height": 475, "x": 580, "y": 580, "screen_num": 1, "msg_colors": {"8": "#e01705", "4": "#f0d400", "2": "#50baeb"}, "filter_str": ["angle overflow", "Recording to drill_nolidar", "Closing drill_nolidar", "Opening drill_nolidar", "Frame processing failed with a TF error"]}}, "water_control": {"is_active": true, "file_name": "gui_modules", "class_name": "DiscreteInput", "init_args": {"text": "Water", "param_name": "dust_collector", "step": 0.05, "allowed_modes": ["drilling", "remote"]}, "prep_args": {"width": 150, "height": 80, "x": 1415, "y": 310, "font_size": "16px", "screen_num": 0}}, "rmo_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextWidget", "init_args": {}, "prep_args": {"text": "RMO ver.", "font_size": "12px", "x": 20, "y": 600, "font_color": "#FFFFFF", "screen": 0, "left": true}}, "first_level_restrictions_off_button": {"is_active": true, "file_name": "gui_modules", "class_name": "EliminateFirstLevelRestrictionsButton", "init_args": {"text": "Eliminate\nRestrictions", "fontSize": 12, "needConfirm": false, "needPassword": true}, "prep_args": {"width": 170, "height": 35, "x": 1410, "y": 725}}, "mover_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "DM ver."}, "prep_args": {"font_size": "12px", "x": 10, "y": 620, "width": 500, "height": 40, "font_color": "#FFFFFF", "screen": 0, "field_to_read": "drill_mover_version", "from_telemetry": true}}, "lidar_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "LS ver."}, "prep_args": {"font_size": "12px", "x": 10, "y": 640, "width": 500, "height": 40, "font_color": "#FFFFFF", "screen": 0, "field_to_read": "lidar_software_version", "from_telemetry": true}}, "hal_version_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "HAL ver."}, "prep_args": {"font_size": "12px", "x": 10, "y": 665, "width": 500, "height": 40, "font_color": "#FFFFFF", "screen": 0, "from_telemetry": false}}, "hal_build_id_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "HAL build ID"}, "prep_args": {"font_size": "12px", "x": 10, "y": 685, "width": 500, "height": 40, "font_color": "#FFFFFF", "screen": 0, "from_telemetry": false}}, "disp_ver_label": {"is_active": true, "file_name": "gui_modules", "class_name": "TextComplicatedWidget", "init_args": {"text": "Disp. ver."}, "prep_args": {"font_size": "12px", "x": 10, "y": 705, "width": 300, "height": 40, "font_color": "#FFFFFF", "screen": 0, "from_telemetry": false}}, "dynamic_action_button": {"is_active": true, "file_name": "gui_modules", "class_name": "DynamicActionButton", "init_args": {"text": "Dynamic Action", "fontSize": 16, "needConfirm": false, "needPassword": false}, "prep_args": {"width": 180, "height": 40, "x": 808, "y": 805}}, "reset_shaft_counter_btn": {"is_active": true, "file_name": "gui_modules", "class_name": "ResetShaftCounterButton", "init_args": {"text": "Reset Counter", "fontSize": 16}, "prep_args": {"width": 180, "height": 40, "x": 606, "y": 850}}}