# -*- coding: utf-8 -*-

from __future__ import annotations

import logging
import os
import threading
import time
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Union

import zenoh
from PyQt6.QtCore import QObject, QThreadPool, QRunnable, pyqtSignal
from rosbags.typesys import Stores, get_typestore, get_types_from_msg

__all__ = ["ZenohClient", "make_msg_header"]

# Configure logger
logger = logging.getLogger("ZenohClient")
logger.setLevel(logging.DEBUG)

# ───────────────────────── helpers ──────────────────────────

def make_msg_header(store, frame_id: str = ""):
    """Return std_msgs/Header with current time."""
    t_ns = time.time_ns()
    sec, nanosec = divmod(t_ns, 1_000_000_000)
    Time = store.types["builtin_interfaces/msg/Time"]
    Header = store.types["std_msgs/msg/Header"]
    return Header(Time(sec, nanosec), frame_id)


class SimpleTask(QRunnable):
    """Run *func* in QThreadPool without additional args."""

    def __init__(self, func: Callable[[], None]):
        super().__init__()
        self.func = func

    def run(self):  # noqa: D401 — Qt naming
        if self.func:
            self.func()


class _SubscriptionHandle:
    """Opaque handle user receives from `subscribe()`."""

    __slots__ = ("key_expr", "callback")

    def __init__(self, key_expr: str, callback: Callable[[str, Any], None]):
        self.key_expr, self.callback = key_expr, callback


# ────────────────────────── main client ─────────────────────────

class ZenohClient(QObject):
    """Qt‑friendly Zenoh/ROS2 client with deduplicated subscriptions."""

    fieldValueReceived = pyqtSignal(str, str, object)
    serviceResponseReceived = pyqtSignal(str, object)

    # ---------------------------------------------------------- init
    def __init__(
        self,
        core: Any,
        msg_dirs: Union[str, List[str]],
        parent: Optional[QObject] = None,
        *,
        max_threads: int = 8,
    ):
        super().__init__(parent)
        self.core = core
        if self.core is not None:
            setattr(self.core, "zenoh_client", self)

        self.msg_dirs = [msg_dirs] if isinstance(msg_dirs, str) else list(msg_dirs)
        self.store = get_typestore(Stores.ROS2_HUMBLE)
        self._register_messages()

        self.cfg = zenoh.Config()
        self.session: Optional[zenoh.Session] = None

        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(max_threads)

        self._routing: Dict[str, List[Callable[[str, Any], None]]] = defaultdict(list)
        self._subscribers: Dict[str, zenoh.Subscriber] = {}
        self._lock = threading.Lock()

        logger.info(
            f"ZenohClient online · {len(self.store.types)} ROS2 types · pool={self.thread_pool.maxThreadCount()}"
        )

    # ------------------------------------------------------ session
    def open(self):
        if self.session is None:
            self.session = zenoh.open(self.cfg)
            logger.debug("Zenoh session opened")

    def prepare(self, msg_dirs: Union[str, List[str]] = None, **prep_args):
        """
        Prepare the ZenohClient when loaded by main loader.

        Args:
            msg_dirs: Optional new message directories to register
        """
        # If new msg_dirs are provided, update and re-register message types
        if msg_dirs:
            self.msg_dirs = [msg_dirs] if isinstance(msg_dirs, str) else list(msg_dirs)
            self._register_messages()
        # Open Zenoh session
        self.open()

    def shutdown(self):
        self.thread_pool.waitForDone()
        if self.session:
            self.session.close()
            self.session = None
            logger.debug("Zenoh session closed")

    # ---------------------------------------------------- registry
    def _register_messages(self):
        count = 0
        for msg_dir in self.msg_dirs:
            if not os.path.exists(msg_dir):
                logger.warning(f"Message directory {msg_dir} not found")
                continue
            pkg = os.path.basename(os.path.dirname(msg_dir))
            kind = os.path.basename(msg_dir)
            for fname in sorted(os.listdir(msg_dir)):
                path = os.path.join(msg_dir, fname)
                if kind == "msg" and fname.endswith(".msg"):
                    full = f"{pkg}/msg/{fname[:-4]}"
                    with open(path) as fh:
                        self.store.register(get_types_from_msg(fh.read(), full))
                    count += 1
                elif kind == "srv" and fname.endswith(".srv"):
                    with open(path) as fh:
                        req, _, resp = fh.read().partition("---")
                    base = f"{pkg}/srv/{fname[:-4]}_"
                    if req.strip():
                        self.store.register(get_types_from_msg(req, base + "Request"))
                    if resp.strip():
                        self.store.register(get_types_from_msg(resp, base + "Response"))
                    count += 1
        logger.info(f"Registered {count} custom ROS2 types")

    # ------------------------------------------------ utilities
    def _resolve_namespace(self, ns: Optional[str]) -> Optional[str]:
        if ns is not None:
            return ns
        if self.core is None:
            return None
        return getattr(self.core, "in_rc_vehid", None) or getattr(self.core, "watched_vehid", None)

    # ------------------------------------------------ subscribe API
    def subscribe(
        self,
        key_expr: str,
        msg_type: str,
        namespace: Optional[str] = None,
        *,
        callback: Callable[[str, Any], None],
    ) -> _SubscriptionHandle:
        ns = self._resolve_namespace(namespace)
        full_key = f"{ns}/{key_expr}" if ns else key_expr
        logger.debug(f"Subscribing to {full_key} with type {msg_type}")

        with self._lock:
            self._routing[full_key].append(callback)
            if full_key not in self._subscribers:
                self.open()

                def _listener(sample, *, _k=full_key, _t=msg_type):
                    try:
                        logger.debug(f"Received raw data on {_k}: {len(sample.payload)} bytes")
                        obj = self.store.deserialize_cdr(sample.payload.to_bytes(), _t)
                        logger.debug(f"Deserialized message: {obj}")
                        for cb in list(self._routing.get(_k, [])):
                            try:
                                cb(str(sample.key_expr), obj)
                            except Exception as e:
                                logger.error(f"Callback error for {_k}: {e}")
                    except Exception as e:
                        logger.error(f"Failed to decode {_t} message: {e}")

                self._subscribers[full_key] = self.session.declare_subscriber(full_key, _listener)

        return _SubscriptionHandle(full_key, callback)

    def unsubscribe(self, handle: _SubscriptionHandle):
        full_key, cb = handle.key_expr, handle.callback
        logger.debug(f"Unsubscribing from {full_key}")

        with self._lock:
            if cb in self._routing.get(full_key, []):
                self._routing[full_key].remove(cb)
            if not self._routing.get(full_key):
                try:
                    self._subscribers.pop(full_key).undeclare()
                except KeyError:
                    logger.warning(f"Attempted to undeclare non-existent subscriber for {full_key}")
                self._routing.pop(full_key, None)

    # ------------------------------------------------------ publish
    def publish(
        self,
        key_expr: str,
        msg_type: str,
        frame_id: str = "",
        namespace: Optional[str] = None,
        *,
        is_joystick: bool = False,
        **fields,
    ) -> None:
        self.open()
        ns = self._resolve_namespace(namespace)
        full_key = f"{ns}/{key_expr}" if ns else key_expr
        logger.debug(f"Publishing to {full_key} with type {msg_type}")

        def _job():
            try:
                hdr = make_msg_header(self.store, frame_id)
                msg = self.store.types[msg_type](header=hdr, **fields)
                raw = self.store.serialize_cdr(msg, msg_type)
                if isinstance(raw, memoryview):
                    raw = raw.tobytes()
                opts = {"priority": 1} if is_joystick else {}
                self.session.put(full_key, raw, encoding=zenoh.Encoding.APPLICATION_CDR, **opts)
            except KeyError:
                logger.error(f"Message type {msg_type} not found in registry")
            except Exception as e:
                logger.error(f"Error publishing to {full_key}: {e}")

        self.thread_pool.start(SimpleTask(_job), 0 if is_joystick else 1)

    # ------------------------------------------------ field helpers
    def get_field(self, key_expr: str, msg_type: str, field_path: str, namespace: Optional[str] = None):
        """Asynchronously get a field value from storage and emit fieldValueReceived signal.

        This method queries Zenoh storage and extracts the specified field from the retrieved message.
        The result will be emitted through the fieldValueReceived signal.
        """
        self.open()
        ns = self._resolve_namespace(namespace)
        full_key = f"{ns}/{key_expr}" if ns else key_expr
        logger.debug(f"Async getting field {field_path} from {full_key}")

        def _task():
            val = None
            try:
                for rep in self.session.get(full_key):
                    if rep.ok:
                        obj = self.store.deserialize_cdr(rep.ok.payload.to_bytes(), msg_type)
                        for attr in field_path.split("."):
                            obj = getattr(obj, attr)
                        val = obj
                        break
            except Exception as e:
                logger.error(f"Error retrieving field {field_path} from {full_key}: {e}")

            self.fieldValueReceived.emit(key_expr, field_path, val)

        self.thread_pool.start(SimpleTask(_task))

    def read_field(self, key_expr: str, msg_type: str, field_path: str, namespace: Optional[str] = None):
        """Synchronously retrieve a field value from storage.

        This method queries Zenoh storage and returns the specified field value directly.
        """
        ns = self._resolve_namespace(namespace)
        full_key = f"{ns}/{key_expr}" if ns else key_expr
        logger.debug(f"Sync reading field {field_path} from {full_key}")

        self.open()
        try:
            for rep in self.session.get(full_key):
                if rep.ok:
                    obj = self.store.deserialize_cdr(rep.ok.payload.to_bytes(), msg_type)
                    for attr in field_path.split("."):
                        obj = getattr(obj, attr)
                    return obj
        except Exception as e:
            logger.error(f"Error reading field {field_path} from {full_key}: {e}")
        return None

    # ------------------------------------------------ service helpers
    def _srv_types(self, pkg: str, srv: str) -> tuple[str, str]:
        """Generate the request and response type strings for a service."""
        base = f"{pkg}/srv/{srv}_"
        return base + "Request", base + "Response"

    def call_service(
        self,
        service_name: str,
        service_pkg: str,
        service_type: str,
        *,
        namespace: Optional[str] = None,
        wait_response: bool = False,
        **req_fields,
    ) -> Optional[Any]:
        """Call a service with the option to wait for a response or fire-and-forget."""
        req_t, resp_t = self._srv_types(service_pkg, service_type)
        ns = self._resolve_namespace(namespace)
        key_base = service_name.lstrip("/")
        srv_key = f"{ns}/{key_base}" if ns else key_base
        logger.debug(f"Calling service {srv_key} with type {service_type}")

        self.open()

        if req_t not in self.store.types:
            logger.error(f"Service request type {req_t} not found in registry")
            return None

        try:
            req_msg = self.store.types[req_t](**req_fields)
            payload = self.store.serialize_cdr(req_msg, req_t)
            if isinstance(payload, memoryview):
                payload = payload.tobytes()
        except Exception as e:
            logger.error(f"Error creating service request for {service_name}: {e}")
            return None

        if not wait_response:
            def _fire():
                try:
                    list(self.session.get(srv_key, payload=payload, encoding=zenoh.Encoding.APPLICATION_CDR))
                except Exception as e:
                    logger.error(f"Async service call to {srv_key} failed: {e}")
            self.thread_pool.start(SimpleTask(_fire))
            return None

        try:
            replies = self.session.get(srv_key, payload=payload, encoding=zenoh.Encoding.APPLICATION_CDR)
            for rep in replies:
                if rep.ok:
                    raw = rep.ok.payload.to_bytes()
                    r_t = resp_t if resp_t in self.store.types else "std_msgs/msg/Empty"
                    resp_obj = self.store.deserialize_cdr(raw, r_t)
                    self.serviceResponseReceived.emit(service_name, resp_obj)
                    return resp_obj
            logger.warning(f"No valid response from service {srv_key}")
        except Exception as e:
            logger.error(f"Sync service call to {srv_key} failed: {e}")
        return None

    def call_service_async(
        self,
        service_name: str,
        service_pkg: str,
        service_type: str,
        *,
        namespace: Optional[str] = None,
        **req_fields,
    ) -> None:
        """Asynchronously call a service and emit serviceResponseReceived when complete."""
        def _task():
            resp = self.call_service(
                service_name,
                service_pkg,
                service_type,
                namespace=namespace,
                wait_response=True,
                **req_fields,
            )
            if resp is None:
                logger.warning(f"Async service call to {service_name} returned None")
        self.thread_pool.start(SimpleTask(_task))

# Example logging configuration
def configure_logging(level=logging.INFO):
    """Configure basic logging for the ZenohClient module."""
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    ))
    logger.addHandler(handler)
    logger.setLevel(level)

# ------------------------------------------------------------------- test code below
if __name__ == "__main__":
    # Configure logging when run directly
    configure_logging(logging.DEBUG)

    from PyQt6.QtCore import QTimer
    from PyQt6.QtWidgets import QApplication

    # ------------------------------------------------------------------- init Qt
    app = QApplication([])

    # ------------------------------------------------------------------- client
    client = ZenohClient(
        core=None,
        msg_dirs=[
            "/Users/<USER>/Work/drill2/onboard/src/drill_msgs/msg",
            "/Users/<USER>/ros2_jazzy/src/ros/ros_tutorials/turtlesim/srv",
        ],
    )

    # ------------------------------------------------------------------- subscribe-пример
    def on_gnss(key: str, msg):
        print(f"GNSS  [{key}]:  {msg}")

    # 🔹 новый API: callback прямо в subscribe()
    gnss_handle = client.subscribe("**/gnss_position",
                                "drill_msgs/msg/GNSS",
                                callback=on_gnss)

    # ------------------------------------------------------------------- сервис-пример
    def on_service_response(srv_name: str, resp):
        print(f"Service response from {srv_name}: {resp}")

    client.serviceResponseReceived.connect(on_service_response)

    # ------------------------------------------------------------------- циклическая нагрузка
    def cyclical_task():
        try:
            # sync storage-GET
            eng_temp = client.read_field("**/engine_state",
                                        "drill_msgs/msg/EngineState",
                                        "engine_fuel_temperature")
            print("engine_fuel_temperature:", eng_temp)

            # async storage-GET
            client.get_field("**/tower_inclinometer",
                            "drill_msgs/msg/Vector2d",
                            "x")

            # publish
            client.publish("tower_control_remote",
                        "drill_msgs/msg/TowerCtrl",
                        frame_id="panel",
                        namespace="local_sim",
                        tilt=0.2, vert_pins=2, incl_pins=1)

            # async service (ответ прилетит в on_service_response)
            client.call_service_async(
                service_name="/turtlesim2/turtle1/teleport_relative",
                service_pkg="turtlesim",
                service_type="TeleportRelative",
                namespace="local_sim",
                linear=0.5,
                angular=0.45,
            )

        except Exception as e:
            logger.error(f"Error in cyclical task: {e}", exc_info=True)

    timer = QTimer()
    timer.timeout.connect(cyclical_task)
    timer.start(2_000)          # каждые 2 с

    # ------------------------------------------------------------------- run
    app.exec()
