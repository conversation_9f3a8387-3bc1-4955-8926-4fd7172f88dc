Running on macOS
Starting application...
/Users/<USER>/Work/iplace-3.0/venv/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
DEBUG:root:Preparing Communicator
DEBUG:root:Module Communicator prepared
DEBUG:root:Preparing ZenohClient
INFO:ZenohClient:Registered 42 custom ROS2 types
INFO:ZenohClient:ZenohClient online · 193 ROS2 types · pool=8
DEBUG:ZenohClient:Zenoh session opened
DEBUG:root:Module ZenohClient prepared
DEBUG:root:Preparing JoystickAdapter
ERROR:root:[Errno 2] could not open port /dev/ttyACM0: [Errno 2] No such file or directory: '/dev/ttyACM0'
ERROR:root:Could not connect to joystick controller /dev/ttyACM0
ERROR:root:[Errno 2] could not open port /dev/ttyACM1: [Errno 2] No such file or directory: '/dev/ttyACM1'
ERROR:root:Could not connect to joystick controller /dev/ttyACM1
ERROR:root:[Errno 2] could not open port /dev/ttyACM2: [Errno 2] No such file or directory: '/dev/ttyACM2'
ERROR:root:Could not connect to joystick controller /dev/ttyACM2
ERROR:root:[Errno 2] could not open port /dev/ttyACM3: [Errno 2] No such file or directory: '/dev/ttyACM3'
ERROR:root:Could not connect to joystick controller /dev/ttyACM3
ERROR:root:[Errno 2] could not open port /dev/ttyACM4: [Errno 2] No such file or directory: '/dev/ttyACM4'
ERROR:root:Could not connect to joystick controller /dev/ttyACM4
INFO:root:Starting UDP-server to accept control data...
INFO:root:Started UDP-server on 0.0.0.0:2000
DEBUG:root:Module JoystickAdapter prepared
DEBUG:root:Preparing PlanReader
DEBUG:root:Module PlanReader prepared
DEBUG:root:Preparing Map
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
DEBUG:root:Module Map prepared
DEBUG:root:Preparing Cameras
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
DEBUG:root:Module Cameras prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing DialIndicator
DEBUG:root:Module DialIndicator prepared
DEBUG:root:Preparing AngleIndicator
QWidget::setLayout: Attempting to set QLayout "" on QMainWindow "", which already has a layout
DEBUG:root:Module AngleIndicator prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CategoryContainer
DEBUG:root:Module CategoryContainer prepared
DEBUG:root:Preparing CircleWidget
DEBUG:root:Module CircleWidget prepared
DEBUG:root:Preparing GPSWidget
DEBUG:root:Module GPSWidget prepared
DEBUG:root:Preparing TextWidget
DEBUG:root:Module TextWidget prepared
DEBUG:root:Preparing MachineStateManagement
DEBUG:root:Module MachineStateManagement prepared
DEBUG:root:Preparing MovingDataTranslator
DEBUG:root:Module MovingDataTranslator prepared
DEBUG:root:Preparing MovingDataTranslator
DEBUG:root:Module MovingDataTranslator prepared
DEBUG:root:Preparing ConditionalTextWidget
DEBUG:root:Module ConditionalTextWidget prepared
DEBUG:root:Preparing CustomSliderWrapper
DEBUG:root:Module CustomSliderWrapper prepared
DEBUG:root:Preparing CustomSliderWrapper
DEBUG:root:Module CustomSliderWrapper prepared
DEBUG:root:Preparing DepthWidgetWrapper
DEBUG:root:Module DepthWidgetWrapper prepared
DEBUG:root:Preparing JoystickWidgetWrapper
DEBUG:root:Module JoystickWidgetWrapper prepared
DEBUG:root:Preparing VehicleSelector
DEBUG:ZenohWidget:Subscribing to main_state (drill_msgs/msg/StateMachineStatus) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/main_state with type drill_msgs/msg/StateMachineStatus
DEBUG:ZenohWidget:Subscribing to permission (drill_msgs/msg/Permission) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/permission with type drill_msgs/msg/Permission
DEBUG:ZenohWidget:Subscribing to robomode (drill_msgs/msg/BoolStamped) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/robomode with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to emergency_status (drill_msgs/msg/BoolStamped) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/emergency_status with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to planner_state (drill_msgs/msg/StateMachineStatus) in namespace rG5
DEBUG:ZenohClient:Subscribing to rG5/planner_state with type drill_msgs/msg/StateMachineStatus
DEBUG:ZenohWidget:Subscribing to main_state (drill_msgs/msg/StateMachineStatus) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/main_state with type drill_msgs/msg/StateMachineStatus
DEBUG:ZenohWidget:Subscribing to permission (drill_msgs/msg/Permission) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/permission with type drill_msgs/msg/Permission
DEBUG:ZenohWidget:Subscribing to robomode (drill_msgs/msg/BoolStamped) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/robomode with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to emergency_status (drill_msgs/msg/BoolStamped) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/emergency_status with type drill_msgs/msg/BoolStamped
DEBUG:ZenohWidget:Subscribing to planner_state (drill_msgs/msg/StateMachineStatus) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/planner_state with type drill_msgs/msg/StateMachineStatus
DEBUG:root:Got WATCH request for vehid None
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:root:Module VehicleSelector prepared
DEBUG:root:Preparing DowntimesButton
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohWidget:Source 'main_state' in ns 'local_sim' freshness changed: True
DEBUG:root:Module DowntimesButton prepared
DEBUG:root:Preparing AuthButton
DEBUG:root:Module AuthButton prepared
DEBUG:root:Preparing DropDrillError
DEBUG:root:Module DropDrillError prepared
DEBUG:root:Preparing SendHoleButton
DEBUG:root:Module SendHoleButton prepared
DEBUG:root:Preparing DropActionButton
DEBUG:root:Module DropActionButton prepared
DEBUG:root:Preparing DropTailingButton
DEBUG:root:Module DropTailingButton prepared
DEBUG:root:Preparing PullupButton
DEBUG:root:Module PullupButton prepared
DEBUG:root:Preparing SmartCheckbox
DEBUG:root:Module SmartCheckbox prepared
DEBUG:root:Preparing SmartButtonGroup
DEBUG:root:Module SmartButtonGroup prepared
DEBUG:root:Preparing SmartButtonGroupForCore
DEBUG:root:Module SmartButtonGroupForCore prepared
DEBUG:root:Preparing PhotoButton
DEBUG:root:Module PhotoButton prepared
DEBUG:root:Preparing SmartButtonGroup
DEBUG:root:Module SmartButtonGroup prepared
DEBUG:root:Preparing SmartButtonGroup
DEBUG:root:Module SmartButtonGroup prepared
DEBUG:root:Preparing MessagesWidget
DEBUG:root:Module MessagesWidget prepared
DEBUG:root:Preparing DiscreteInput
DEBUG:root:Module DiscreteInput prepared
DEBUG:root:Preparing TextWidget
DEBUG:root:Module TextWidget prepared
DEBUG:root:Preparing EliminateFirstLevelRestrictionsButton
DEBUG:root:Module EliminateFirstLevelRestrictionsButton prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing TextComplicatedWidget
DEBUG:root:Module TextComplicatedWidget prepared
DEBUG:root:Preparing DynamicActionButton
DEBUG:root:Module DynamicActionButton prepared
DEBUG:root:Preparing ResetShaftCounterButton
DEBUG:root:Module ResetShaftCounterButton prepared
ERROR:root:Error starting module ZenohClient: 'ZenohClient' object has no attribute 'start'
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
qt.qpa.fonts: Populating font family aliases took 71 ms. Replace uses of missing font family "Roboto" with one that exists to avoid this cost. 
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:root:Got WATCH request for vehid local_sim
DEBUG:ZenohWidget:Setting up service source 'route_segments' for service /get_current_drive_action
DEBUG:ZenohWidget:Subscribing to position (drill_msgs/msg/Position) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/position with type drill_msgs/msg/Position
DEBUG:ZenohWidget:Subscribing to driver_status (drill_msgs/msg/DriveStatus) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/driver_status with type drill_msgs/msg/DriveStatus
DEBUG:ZenohWidget:Subscribing to obstacles_vis (visualization_msgs/msg/MarkerArray) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/obstacles_vis with type visualization_msgs/msg/MarkerArray
DEBUG:ZenohWidget:Subscribing to safety_berm_map (visualization_msgs/msg/MarkerArray) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/safety_berm_map with type visualization_msgs/msg/MarkerArray
DEBUG:ZenohWidget:Subscribing to border_vis (visualization_msgs/msg/MarkerArray) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/border_vis with type visualization_msgs/msg/MarkerArray
DEBUG:ZenohWidget:Subscribing to level (drill_msgs/msg/Level) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/level with type drill_msgs/msg/Level
DEBUG:ZenohWidget:Subscribing to tower_inclinometer (drill_msgs/msg/Vector2d) in namespace local_sim
DEBUG:ZenohClient:Subscribing to local_sim/tower_inclinometer with type drill_msgs/msg/Vector2d
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
2025-05-28 22:11:23,009 [ERROR] gui_modules.controls.vehicle_selector (vehicle_selector.py:468): No configuration for action auto
ERROR:gui_modules.controls.vehicle_selector:No configuration for action auto
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=14352000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=16.161521911621094, y=15.838478088378906, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=14352000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=16.161521911621094, y=15.838478088378906, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 16.161521911621094
DEBUG:ZenohWidget:Source 'position_x' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 15.838478088378906
DEBUG:ZenohWidget:Source 'position_y' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Source 'yaw' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 16.161521911621094
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 15.838478088378906
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=121871000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=16.090810775756836, y=15.909188270568848, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=121871000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=16.090810775756836, y=15.909188270568848, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 16.090810775756836
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 15.909188270568848
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 16.090810775756836
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 15.909188270568848
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=143531000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=143531000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:Source 'driver_action_id' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
DEBUG:ZenohWidget:Source 'driver_segment_id' in ns 'local_sim' freshness changed: True
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 2
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
DEBUG:ZenohWidget:Source 'driver_point_id' in ns 'local_sim' freshness changed: True
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:Calling service /get_current_drive_action with type drill_msgs/srv/GetCurrentDriveAction in namespace local_sim
DEBUG:ZenohClient:Calling service local_sim/get_current_drive_action with type GetCurrentDriveAction
DEBUG:ZenohWidget:Service response handler connected
DEBUG:ZenohWidget:Calling service /get_current_drive_action with type drill_msgs/srv/GetCurrentDriveAction in namespace local_sim
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 2
DEBUG:ZenohClient:Calling service local_sim/get_current_drive_action with type GetCurrentDriveAction
ERROR:ZenohClient:Sync service call to local_sim/get_current_drive_action failed: 'drill_msgs/srv/msg/Path'
ERROR:ZenohClient:Sync service call to local_sim/get_current_drive_action failed: 'drill_msgs/srv/msg/Path'
WARNING:ZenohClient:Async service call to /get_current_drive_action returned None
WARNING:ZenohClient:Async service call to /get_current_drive_action returned None
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=223117000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=16.02010154724121, y=15.979899406433105, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=223117000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=16.02010154724121, y=15.979899406433105, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 16.02010154724121
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 15.979899406433105
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 16.02010154724121
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 15.979899406433105
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=333053000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.949390411376953, y=16.050609588623047, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=333053000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.949390411376953, y=16.050609588623047, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.949390411376953
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.050609588623047
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.949390411376953
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.050609588623047
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=433572000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.878679275512695, y=16.121320724487305, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=433572000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.878679275512695, y=16.121320724487305, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.878679275512695
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.121320724487305
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.878679275512695
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.121320724487305
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=543596000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.807969093322754, y=16.192031860351562, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=543596000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.807969093322754, y=16.192031860351562, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.807969093322754
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.192031860351562
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.807969093322754
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.192031860351562
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=653806000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.737257957458496, y=16.262741088867188, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=653806000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.737257957458496, y=16.262741088867188, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.737257957458496
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.262741088867188
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.737257957458496
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.262741088867188
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
dev_mode is : True
PrepCam
call set mode
setting mode  front
mode set front
setting mode  front
mode set front
text color is:
smart button init
smart button init
smart button init
smart button init
smart button init
fontSize is not None:  True
fontSize is not None:  True
smart button init
fontSize is not None:  True
fontSize is not None:  True
smart button init
smart button init
smart button init
Namespace changed to 'local_sim', routes cache cleared
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Driver action ID changed: None -> 13
Requested routes for action ID: 13
Action ID changed: None -> 13
Requested routes for action ID: 13
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=762934000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.666547775268555, y=16.333452224731445, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=762934000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.666547775268555, y=16.333452224731445, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.666547775268555
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.333452224731445
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.666547775268555
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.333452224731445
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=863539000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.595836639404297, y=16.404163360595703, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=863539000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.595836639404297, y=16.404163360595703, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.595836639404297
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.404163360595703
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.595836639404297
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.404163360595703
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973049000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.525126457214355, y=16.47487449645996, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973049000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.525126457214355, y=16.47487449645996, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.525126457214355
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.47487449645996
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.525126457214355
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.47487449645996
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=73512000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.454415321350098, y=16.545583724975586, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=73512000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.454415321350098, y=16.545583724975586, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.454415321350098
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.545583724975586
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.454415321350098
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.545583724975586
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=153584000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=153584000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 2
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=181929000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.383705139160156, y=16.616294860839844, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=181929000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.383705139160156, y=16.616294860839844, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.383705139160156
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.616294860839844
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.383705139160156
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.616294860839844
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=283453000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.312994003295898, y=16.6870059967041, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=283453000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.312994003295898, y=16.6870059967041, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.312994003295898
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.6870059967041
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.312994003295898
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.6870059967041
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=393494000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.242283821105957, y=16.75771713256836, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=393494000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.242283821105957, y=16.75771713256836, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.242283821105957
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.75771713256836
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.242283821105957
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.75771713256836
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=502792000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.1715726852417, y=16.828426361083984, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=502792000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.1715726852417, y=16.828426361083984, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.1715726852417
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.828426361083984
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.1715726852417
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.828426361083984
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=603545000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.100862503051758, y=16.899137496948242, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=603545000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.100862503051758, y=16.899137496948242, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.100862503051758
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.899137496948242
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.100862503051758
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.899137496948242
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=712679000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=712679000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=813613000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=813613000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922617000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922617000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=23223000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=23223000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=123384000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=123384000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=162071000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=162071000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=2, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 2
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=233361000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=233361000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=343004000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=343004000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=443431000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=443431000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=15.0301513671875, y=16.9698486328125, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 15.0301513671875
CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 16.9698486328125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 15.0301513671875
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 16.9698486328125
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=543758000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.959441184997559, y=17.040559768676758, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=543758000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.959441184997559, y=17.040559768676758, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.959441184997559
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.040559768676758
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.959441184997559
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.040559768676758
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=653402000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.8887300491333, y=17.111268997192383, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=653402000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.8887300491333, y=17.111268997192383, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.8887300491333
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.111268997192383
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.8887300491333
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.111268997192383
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=762653000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.81801986694336, y=17.18198013305664, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=762653000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.81801986694336, y=17.18198013305664, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.81801986694336
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.18198013305664
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.81801986694336
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.18198013305664
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=863543000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.747308731079102, y=17.2526912689209, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=863543000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.747308731079102, y=17.2526912689209, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.747308731079102
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.2526912689209
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.747308731079102
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.2526912689209
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973337000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.67659854888916, y=17.323402404785156, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973337000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.67659854888916, y=17.323402404785156, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.67659854888916
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.323402404785156
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.67659854888916
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.323402404785156
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=83044000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.605887413024902, y=17.39411163330078, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=83044000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.605887413024902, y=17.39411163330078, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.605887413024902
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.39411163330078
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.605887413024902
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.39411163330078
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
WARNING:root:Transition to CardStatus.want_auto timed out for vehicle local_sim
2025-05-28 22:11:26,143 [ERROR] gui_modules.controls.vehicle_selector (vehicle_selector.py:839): Transition CardStatus.want_auto timed out for vehicle local_sim
ERROR:gui_modules.controls.vehicle_selector:Transition CardStatus.want_auto timed out for vehicle local_sim
qt.qpa.drawing: Layer-backing is always enabled.  QT_MAC_WANTS_LAYER/_q_mac_wantsLayer has no effect.
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=163357000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=3, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=163357000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=3, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 3
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=183528000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.535177230834961, y=17.46482276916504, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=183528000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.535177230834961, y=17.46482276916504, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.535177230834961
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.46482276916504
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 3
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.535177230834961
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.46482276916504
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=293516000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.464466094970703, y=17.535533905029297, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=293516000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.464466094970703, y=17.535533905029297, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.464466094970703
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.535533905029297
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.464466094970703
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.535533905029297
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=403624000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.393754959106445, y=17.606245040893555, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=403624000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.393754959106445, y=17.606245040893555, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.393754959106445
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.606245040893555
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.393754959106445
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.606245040893555
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=513485000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.323044776916504, y=17.676956176757812, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=513485000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.323044776916504, y=17.676956176757812, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.323044776916504
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.676956176757812
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.323044776916504
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.676956176757812
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=623600000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.252333641052246, y=17.747665405273438, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=623600000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.252333641052246, y=17.747665405273438, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.252333641052246
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.747665405273438
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.252333641052246
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.747665405273438
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=731634000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.181623458862305, y=17.818376541137695, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=731634000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.181623458862305, y=17.818376541137695, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.181623458862305
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.818376541137695
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.181623458862305
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.818376541137695
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=833300000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.110912322998047, y=17.889087677001953, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=833300000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.110912322998047, y=17.889087677001953, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.110912322998047
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.889087677001953
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.110912322998047
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.889087677001953
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=943319000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.040202140808105, y=17.95979881286621, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459486, nanosec=943319000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=14.040202140808105, y=17.95979881286621, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 14.040202140808105
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 17.95979881286621
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 14.040202140808105
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 17.95979881286621
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=43490000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.969491004943848, y=18.030508041381836, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=43490000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.969491004943848, y=18.030508041381836, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.969491004943848
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.030508041381836
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.969491004943848
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.030508041381836
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=153599000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.898780822753906, y=18.101219177246094, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=153599000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.898780822753906, y=18.101219177246094, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.898780822753906
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.101219177246094
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.898780822753906
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.101219177246094
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=173673000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=3, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=173673000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=3, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 3
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 3
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=262419000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.828069686889648, y=18.17193031311035, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=262419000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.828069686889648, y=18.17193031311035, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.828069686889648
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.17193031311035
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.828069686889648
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.17193031311035
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=363226000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.757359504699707, y=18.24264144897461, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=363226000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.757359504699707, y=18.24264144897461, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.757359504699707
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.24264144897461
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.757359504699707
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.24264144897461
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=463456000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.68664836883545, y=18.313350677490234, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=463456000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.68664836883545, y=18.313350677490234, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.68664836883545
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.313350677490234
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.68664836883545
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.313350677490234
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=563611000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.615938186645508, y=18.384061813354492, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=563611000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.615938186645508, y=18.384061813354492, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.615938186645508
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.384061813354492
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.615938186645508
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.384061813354492
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=672380000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=672380000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=773408000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=773408000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
INFO:root:Connected to rG5
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=883537000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=883537000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=993101000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=1748459487, nanosec=993101000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=102866000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=102866000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=183543000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=3, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=183543000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=3, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 3
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 3
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=203095000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=203095000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=303542000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=303542000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=413598000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=413598000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.54522705078125, y=18.45477294921875, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.54522705078125
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.45477294921875
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=523417000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.474516868591309, y=18.525484085083008, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=523417000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.474516868591309, y=18.525484085083008, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.474516868591309
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.525484085083008
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.474516868591309
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.525484085083008
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=631807000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.40380573272705, y=18.596193313598633, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=631807000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.40380573272705, y=18.596193313598633, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.40380573272705
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.596193313598633
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.40380573272705
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.596193313598633
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=732929000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.33309555053711, y=18.66690444946289, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=732929000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.33309555053711, y=18.66690444946289, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.33309555053711
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.66690444946289
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.33309555053711
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.66690444946289
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=833515000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.262384414672852, y=18.73761558532715, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=833515000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.262384414672852, y=18.73761558532715, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.262384414672852
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.73761558532715
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.262384414672852
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.73761558532715
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=935121000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.19167423248291, y=18.808326721191406, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=935121000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.19167423248291, y=18.808326721191406, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.19167423248291
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.19167423248291
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.808326721191406
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.808326721191406
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
Error getting telemetry for PlannerNode: 'PlannerNode'
No data for  PlannerNode
Error getting telemetry for MainStateMachineNode: 'MainStateMachineNode'
No data for  MainStateMachineNode
Error getting telemetry for RodChangerNode: 'RodChangerNode'
No data for  RodChangerNode
Error getting telemetry for ForkControlNode: 'ForkControlNode'
No data for  ForkControlNode
Error getting telemetry for CarouselControllerNode: 'CarouselControllerNode'
No data for  CarouselControllerNode
Error getting telemetry for WrenchControllerNode: 'WrenchControllerNode'
No data for  WrenchControllerNode
Error getting telemetry for DrillerNode: 'DrillerNode'
No data for  DrillerNode
Error getting telemetry for ArmControllerNode: 'ArmControllerNode'
No data for  ArmControllerNode
Error getting telemetry for DustFlapsControllerNode: 'DustFlapsControllerNode'
No data for  DustFlapsControllerNode
Error getting telemetry for LevelerNode: 'LevelerNode'
No data for  LevelerNode
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=44197000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.120963096618652, y=18.87903594970703, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=44197000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.120963096618652, y=18.87903594970703, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.120963096618652
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.87903594970703
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.120963096618652
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.87903594970703
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:downtimes:Vehicle data missing 'vehid' key: {}
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=153760000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.050252914428711, y=18.94974708557129, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=153760000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=13.050252914428711, y=18.94974708557129, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 13.050252914428711
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 18.94974708557129
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 13.050252914428711
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 18.94974708557129
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/driver_status: 52 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=193289000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=4, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:Received message on local_sim/driver_status: drill_msgs__msg__DriveStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=193289000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=13, last_action_id=12, cur_segment_id=2, cur_point_id=4, status='executing', __msgtype__='drill_msgs/msg/DriveStatus')
DEBUG:ZenohWidget:  - Extracted field 'cur_action_id' for source 'driver_action_id': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_segment_id' for source 'driver_segment_id': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_action_id' in ns 'local_sim': 13
DEBUG:ZenohWidget:  - Extracted field 'cur_point_id' for source 'driver_point_id': 4
DEBUG:ZenohWidget:Dispatching update for 'driver_segment_id' in ns 'local_sim': 2
DEBUG:ZenohWidget:Dispatching update for 'driver_point_id' in ns 'local_sim': 4
DEBUG:ZenohClient:Publishing to rG5/rmo_health with type drill_msgs/msg/RmoHealth
DEBUG:ZenohClient:Publishing to local_sim/rmo_health with type drill_msgs/msg/RmoHealth
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=261958000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.979541778564453, y=19.020458221435547, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=261958000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.979541778564453, y=19.020458221435547, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.979541778564453
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.020458221435547
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 12.979541778564453
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 19.020458221435547
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=363578000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.908831596374512, y=19.091169357299805, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=363578000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.908831596374512, y=19.091169357299805, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.908831596374512
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.091169357299805
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 12.908831596374512
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 19.091169357299805
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=473400000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.838120460510254, y=19.16187858581543, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=473400000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.838120460510254, y=19.16187858581543, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.838120460510254
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.16187858581543
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 12.838120460510254
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 19.16187858581543
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: [Errno 54] Connection reset by peer
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=582850000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.767410278320312, y=19.232589721679688, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=582850000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.767410278320312, y=19.232589721679688, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.767410278320312
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.232589721679688
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 12.767410278320312
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 19.232589721679688
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 461, in checkboxTimerAction
    newState = self.core.telemetry[vehid]['channels_states'][self.paramName]
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=683686000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.696699142456055, y=19.303300857543945, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=683686000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.696699142456055, y=19.303300857543945, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.696699142456055
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.303300857543945
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 12.696699142456055
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 19.303300857543945
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/customClasses/smartButton.py", line 342, in timerAction
    if vehid is not None and self.paramName in self.core.telemetry[vehid]['channels_states']:
KeyError: 'channels_states'
INFO:root:Connected to rG5
ERROR:root:Socket error: Connection closed or empty response
ERROR:root:No connection to vehicle rG5
WARNING:root:Unknown response type (None) from veh #rG5
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=783894000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.625988006591797, y=19.374011993408203, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=783894000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.625988006591797, y=19.374011993408203, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.625988006591797
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.374011993408203
DEBUG:ZenohWidget:Dispatching update for 'position_x' in ns 'local_sim': 12.625988006591797
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohWidget:Dispatching update for 'position_y' in ns 'local_sim': 19.374011993408203
DEBUG:ZenohWidget:Dispatching update for 'yaw' in ns 'local_sim': 135.0
Traceback (most recent call last):
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 368, in show
    self.load_new_stream(watched_vehid)
  File "/Users/<USER>/Work/iplace-3.0/core/gui_modules/cameras/cameras.py", line 260, in load_new_stream
    if self.cam_id not in cam_configs:
TypeError: argument of type 'NoneType' is not iterable
INFO:root:halt event
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883878000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.555277824401855, y=19.44472312927246, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=883878000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.555277824401855, y=19.44472312927246, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.555277824401855
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.44472312927246
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohClient:Received raw data on local_sim/main_state: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:Received message on local_sim/main_state: drill_msgs__msg__StateMachineStatus(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), frame_id='', __msgtype__='std_msgs/msg/Header'), cur_action_id=0, last_action_id=0, current_state='remote', __msgtype__='drill_msgs/msg/StateMachineStatus')
DEBUG:ZenohWidget:  - Extracted field 'current_state' for source 'main_state': remote
DEBUG:ZenohClient:Received raw data on local_sim/position: 40 bytes
DEBUG:ZenohClient:Deserialized message: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=993300000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.484566688537598, y=19.515432357788086, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:Received message on local_sim/position: drill_msgs__msg__Position(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=993300000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='map', __msgtype__='std_msgs/msg/Header'), x=12.484566688537598, y=19.515432357788086, z=0.0, yaw=135.0, is_reliable=True, __msgtype__='drill_msgs/msg/Position')
DEBUG:ZenohWidget:  - Extracted field 'x' for source 'position_x': 12.484566688537598
DEBUG:ZenohWidget:  - Extracted field 'y' for source 'position_y': 19.515432357788086
DEBUG:ZenohWidget:  - Extracted field 'yaw' for source 'yaw': 135.0
DEBUG:ZenohClient:Unsubscribing from local_sim/position
DEBUG:ZenohClient:Unsubscribing from local_sim/driver_status
DEBUG:ZenohClient:Unsubscribing from local_sim/obstacles_vis
DEBUG:ZenohClient:Unsubscribing from local_sim/safety_berm_map
DEBUG:ZenohClient:Unsubscribing from local_sim/border_vis
DEBUG:ZenohClient:Unsubscribing from local_sim/level
DEBUG:ZenohClient:Unsubscribing from local_sim/tower_inclinometer
./run_app.sh: line 31: 76140 Terminated: 15          python3 main.py dev_mode
/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py:216: UserWarning: resource_tracker: There appear to be 3 leaked semaphore objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
